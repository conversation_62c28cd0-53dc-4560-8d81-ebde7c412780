-- Migration: Optimize RBAC Performance
-- This migration optimizes the RBAC implementation to follow Supabase best practices
-- for performance and security

-- 1. <PERSON>reate optimized authorize function
CREATE OR REPLACE FUNCTION public.authorize(
  requested_permission app_permission
) RETURNS BOOLEAN AS $$
DECLARE
  bind_permissions INT;
  user_role public.app_role;
  user_provider_role public.provider_role_type;
  jwt_claims JSONB;
BEGIN
  -- Get JWT claims once and cache them (performance optimization)
  SELECT (SELECT auth.jwt()) INTO jwt_claims;
  
  -- Extract role information from cached JWT
  SELECT (jwt_claims ->> 'user_role')::public.app_role INTO user_role;
  SELECT (jwt_claims ->> 'provider_role')::public.provider_role_type INTO user_provider_role;

  -- For catering_provider role, check provider sub-role permissions
  IF user_role = 'catering_provider' AND user_provider_role IS NOT NULL THEN
    -- Count matching permissions for the user's provider sub-role
    SELECT COUNT(*)
    INTO bind_permissions
    FROM public.provider_role_permissions
    WHERE provider_role_permissions.permission = requested_permission
      AND provider_role_permissions.provider_role = user_provider_role;
  ELSE
    -- For other roles (admin, user), use the standard role permissions
    SELECT COUNT(*)
    INTO bind_permissions
    FROM public.role_permissions
    WHERE role_permissions.permission = requested_permission
      AND role_permissions.role = user_role;
  END IF;

  RETURN bind_permissions > 0;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

-- 2. Create alias function for backward compatibility
CREATE OR REPLACE FUNCTION public.has_permission(
  requested_permission app_permission
) RETURNS BOOLEAN AS $$
BEGIN
  RETURN authorize(requested_permission);
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER
SET search_path = '';

-- 3. Create performance indexes for RLS policies
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON public.user_roles (user_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_permission ON public.role_permissions (role, permission);
CREATE INDEX IF NOT EXISTS idx_provider_role_permissions_provider_role_permission ON public.provider_role_permissions (provider_role, permission);
CREATE INDEX IF NOT EXISTS idx_profiles_id ON public.profiles (id);

-- 4. Drop existing policies to recreate them with optimizations
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Admins can update any profile" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_policy" ON public.profiles;

-- 5. Create optimized profiles policies
CREATE POLICY "profiles_select_policy"
  ON public.profiles FOR SELECT
  TO authenticated
  USING ((id = (SELECT auth.uid())) OR (SELECT authorize('users.read'::app_permission)));

CREATE POLICY "profiles_update_policy"
  ON public.profiles FOR UPDATE
  TO authenticated
  USING ((id = (SELECT auth.uid())) OR (SELECT authorize('users.write'::app_permission)));

CREATE POLICY "profiles_insert_policy"
  ON public.profiles FOR INSERT
  TO authenticated
  WITH CHECK ((SELECT authorize('users.write'::app_permission)));

CREATE POLICY "profiles_delete_policy"
  ON public.profiles FOR DELETE
  TO authenticated
  USING ((SELECT authorize('users.delete'::app_permission)));

-- 6. Drop existing user_roles policies
DROP POLICY IF EXISTS "Users can view their own roles" ON public.user_roles;
DROP POLICY IF EXISTS "Admins can view all user roles" ON public.user_roles;
DROP POLICY IF EXISTS "Admins can manage user roles" ON public.user_roles;
DROP POLICY IF EXISTS "user_roles_select_policy" ON public.user_roles;
DROP POLICY IF EXISTS "user_roles_insert_policy" ON public.user_roles;
DROP POLICY IF EXISTS "user_roles_update_policy" ON public.user_roles;
DROP POLICY IF EXISTS "user_roles_delete_policy" ON public.user_roles;

-- 7. Create optimized user_roles policies
CREATE POLICY "user_roles_select_policy"
  ON public.user_roles FOR SELECT
  TO authenticated
  USING ((user_id = (SELECT auth.uid())) OR (SELECT authorize('users.read'::app_permission)));

CREATE POLICY "user_roles_insert_policy"
  ON public.user_roles FOR INSERT
  TO authenticated
  WITH CHECK ((SELECT authorize('users.write'::app_permission)));

CREATE POLICY "user_roles_update_policy"
  ON public.user_roles FOR UPDATE
  TO authenticated
  USING ((SELECT authorize('users.write'::app_permission)))
  WITH CHECK ((SELECT authorize('users.write'::app_permission)));

CREATE POLICY "user_roles_delete_policy"
  ON public.user_roles FOR DELETE
  TO authenticated
  USING ((SELECT authorize('users.write'::app_permission)));

-- Keep the auth hook policy
-- CREATE POLICY "auth_hook_can_read_user_roles" already exists

-- 8. Drop existing role_permissions policies
DROP POLICY IF EXISTS "All authenticated users can view role permissions" ON public.role_permissions;
DROP POLICY IF EXISTS "Admins can manage role permissions" ON public.role_permissions;
DROP POLICY IF EXISTS "All authenticated users can read role permissions" ON public.role_permissions;
DROP POLICY IF EXISTS "role_permissions_insert_policy" ON public.role_permissions;
DROP POLICY IF EXISTS "role_permissions_update_policy" ON public.role_permissions;
DROP POLICY IF EXISTS "role_permissions_delete_policy" ON public.role_permissions;

-- 9. Create optimized role_permissions policies
CREATE POLICY "All authenticated users can read role permissions"
  ON public.role_permissions FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "role_permissions_insert_policy"
  ON public.role_permissions FOR INSERT
  TO authenticated
  WITH CHECK ((SELECT authorize('settings.write'::app_permission)));

CREATE POLICY "role_permissions_update_policy"
  ON public.role_permissions FOR UPDATE
  TO authenticated
  USING ((SELECT authorize('settings.write'::app_permission)))
  WITH CHECK ((SELECT authorize('settings.write'::app_permission)));

CREATE POLICY "role_permissions_delete_policy"
  ON public.role_permissions FOR DELETE
  TO authenticated
  USING ((SELECT authorize('settings.write'::app_permission)));

-- 10. Drop existing provider_role_permissions policies
DROP POLICY IF EXISTS "provider_role_permissions_consolidated_select_policy" ON public.provider_role_permissions;
DROP POLICY IF EXISTS "provider_role_permissions_admin_insert_policy" ON public.provider_role_permissions;
DROP POLICY IF EXISTS "provider_role_permissions_admin_update_policy" ON public.provider_role_permissions;
DROP POLICY IF EXISTS "provider_role_permissions_admin_delete_policy" ON public.provider_role_permissions;
DROP POLICY IF EXISTS "All authenticated users can read provider permissions" ON public.provider_role_permissions;
DROP POLICY IF EXISTS "provider_role_permissions_insert_policy" ON public.provider_role_permissions;
DROP POLICY IF EXISTS "provider_role_permissions_update_policy" ON public.provider_role_permissions;
DROP POLICY IF EXISTS "provider_role_permissions_delete_policy" ON public.provider_role_permissions;

-- 11. Create optimized provider_role_permissions policies
CREATE POLICY "All authenticated users can read provider permissions"
  ON public.provider_role_permissions FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "provider_role_permissions_insert_policy"
  ON public.provider_role_permissions FOR INSERT
  TO authenticated
  WITH CHECK ((SELECT authorize('settings.write'::app_permission)));

CREATE POLICY "provider_role_permissions_update_policy"
  ON public.provider_role_permissions FOR UPDATE
  TO authenticated
  USING ((SELECT authorize('settings.write'::app_permission)))
  WITH CHECK ((SELECT authorize('settings.write'::app_permission)));

CREATE POLICY "provider_role_permissions_delete_policy"
  ON public.provider_role_permissions FOR DELETE
  TO authenticated
  USING ((SELECT authorize('settings.write'::app_permission)));
